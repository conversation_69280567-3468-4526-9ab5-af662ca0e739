import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/chat_cubit.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/chat/chat_state.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class ChatPage extends BaseBlocProvider<ChatState, ChatCubit> {
  const ChatPage({required this.parameter, super.key});

  final ChatParameter parameter;

  @override
  Widget buildPage() => const ChatView();

  @override
  ChatCubit createCubit() => ChatCubit(
        activityRepository: locator.get(),
        homeCubit: locator.get(),
        parameter: parameter,
      );
}

class ChatView extends StatefulWidget {
  const ChatView({super.key});

  @override
  State<ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends BaseBlocPageState<ChatView, ChatState, ChatCubit>
    with TickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _micAnimationController;
  late AnimationController _blinkAnimationController;
  late Animation<double> _blinkAnimation;

  // to manage a list of susggestions that the user has selected
  List<String> _selectedSuggestions = [];
  // manage the hint
  bool _hintShown = false;
  // track if timeout dialog is showing
  bool _isTimeoutDialogShowing = false;

  @override
  void initState() {
    super.initState();
    _micAnimationController = AnimationController(
        vsync: this,
        lowerBound: 0.0,
        upperBound: 1.0,
        animationBehavior: AnimationBehavior.normal);
    _blinkAnimationController = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 500),
        reverseDuration: const Duration(milliseconds: 500));
    _blinkAnimation =
        Tween<double>(begin: 1.0, end: 0.0).animate(_blinkAnimationController);

    _controller.addListener(_onTextChanged);

    _checkHintDialogSettings();
  }

  Future<void> _checkHintDialogSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final permanentlyHidden =
        prefs.getBool('suggestionHintPermanentlyHidden') ?? false;
    if (permanentlyHidden) {
      setState(() {
        _hintShown = true;
      });
    }
  }

  void _onTextChanged() {
    // do something
    if (_controller.text.isEmpty) {
      setState(() {
        _selectedSuggestions.clear();
      });
    }

    //for each selectedSuggestions, check if it is still in the text, if not remove it
    setState(() {
      _selectedSuggestions
          .removeWhere((element) => !_controller.text.contains(element));
    });
  }

  @override
  bool listenWhen(ChatState previous, ChatState current) {
    if (current.connState == ConnState.timeout) {
      _showTimeoutDialog();
    } else {
      _scrollToBottom();
    }

    if (previous.isRecording != current.isRecording) {
      if (current.isRecording) {
        _micAnimationController.repeat(
            reverse: true, period: const Duration(milliseconds: 500));
        _blinkAnimationController.repeat(reverse: true);
      } else if (current.connState == ConnState.error) {
        _showChatErrorDialog();
      } else {
        _micAnimationController.reset();
        _blinkAnimationController.reset();
      }
    }

    // Check if aiTripIntent has been updated
    if (previous.aiTripIntent == null && current.aiTripIntent != null) {
      // Auto-navigate to VTP if intent is detected
      // Uncomment the line below if you want automatic navigation
      _navigateToVisualTripPlanner(context.read<ChatCubit>(), current);
    }

    return super.listenWhen(previous, current);
  }

  void _handleSuggestionPressed(
      String suggestion, ChatCubit cubit, ChatState state) {
    String fullResponse = suggestion;
    if (_selectedSuggestions.isNotEmpty) {
      fullResponse = _selectedSuggestions.join(', ') + ', ' + suggestion;
    }

    // XXX: careful when update controller.text, because there's a listener on the controller, will affect the _selectedSuggestions
    _controller.text = fullResponse;
    _sendMessage(cubit, state);
  }

  void _handleSuggestionLongPressed(String suggestion) {
    setState(() {
      _selectedSuggestions.add(suggestion);
      _controller.text = _selectedSuggestions.join(', ');
    });
  }

  void _sendMessage(ChatCubit cubit, ChatState state) {
    if (_controller.text.isNotEmpty || state.base64Image != null) {
      final message = _controller.text;
      cubit.sendMessage(message, state.base64Image, state.fileName);
      _controller.clear();
      setState(() {
        _selectedSuggestions.clear();
      });
      _scrollToBottom();
    }
  }

  void _goBack() {
    // _closeWebSocketChannel();
    // Navigator.of(context).pushReplacement(
    //   MaterialPageRoute(builder: (context) => LoginScreen()),
    // );
    Navigator.of(context).pop();
  }

  void _goBackFromDialog() {
    // _closeWebSocketChannel();
    final cubit = context.read<ChatCubit>();
    cubit.resetConnectionState();
    Navigator.of(context).pop(); //close the dialog
    //Navigator.of(context).pushReplacement(
    //  MaterialPageRoute(builder: (context) => LoginScreen()),
    //);
  }

  void _showSuggestionHelpDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                const BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10.0,
                  offset: Offset(0.0, 10.0),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with icon
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        shape: BoxShape.circle,
                      ),
                      child:
                          Icon(Icons.lightbulb, color: Colors.blue, size: 30),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                      child: Text(
                        LocaleKeys.hints_title.tr(),
                        style: AppStyle.bold22V2(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Long press gesture illustration
                _buildGestureExplanation(
                  icon: Icons.touch_app,
                  iconColor: Colors.orange,
                  title: LocaleKeys.long_press_title.tr(),
                  description: LocaleKeys.hints_long_press.tr(),
                ),

                const SizedBox(height: 16),

                // Tap gesture illustration
                _buildGestureExplanation(
                  icon: Icons.tap_and_play,
                  iconColor: Colors.green,
                  title: LocaleKeys.short_press_title.tr(),
                  description: LocaleKeys.hints_short_press.tr(),
                ),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 10),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      onPressed: () async {
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool(
                            'suggestionHintPermanentlyHidden', true);
                        setState(() {
                          _hintShown = true;
                        });
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        LocaleKeys.got_it.tr(),
                        style: AppStyle.bold16V2(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGestureExplanation({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: .1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: iconColor),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppStyle.regular16V2(),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppStyle.regular14V2(
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showChatErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocaleKeys.error_title.tr()),
        content: Text(LocaleKeys.error_content.tr()),
        actions: [
          TextButton(
            onPressed: _goBackFromDialog,
            child: Text(LocaleKeys.ok_text.tr()),
          ),
          TextButton(
            onPressed: () {
              // Share feedback

              // get the messages[] which content the user's  chat history , and share as text via the usual app Share options :
              // .  email, chat , imessage, whatsapp, etc
              //XXX: TODO
            },
            child: Text(LocaleKeys.share_feedback_text.tr()),
          ),
        ],
      ),
    );
  }

  void _showTimeoutDialog() {
    if (_isTimeoutDialogShowing) return;

    _isTimeoutDialogShowing = true;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocaleKeys.timeout_title.tr()),
        content: Text(LocaleKeys.timeout_content.tr()),
        actions: [
          TextButton(
            onPressed: _goBackFromDialog,
            child: Text(LocaleKeys.ok_text.tr()),
          ),
        ],
      ),
    );
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showTripDetails(ChatCubit cubit, ChatState state) {
    if (state.aiTrip == null) return;

    context.router
        .push(TripRoute(parameter: TripParameter(trip: state.aiTrip!)));
  }

  void _showEventDetails(
      ChatCubit cubit, ChatState state, Map<String, dynamic> message) async {
    if (state.aiEvent == null) return;
    var newEvent = message["newEvent"] as EventModels;
    final result = await context.pushRoute(UpsertEventRoute(
        upsertEventParameter: UpsertEventParameter(model: newEvent)));
    if (result is EventModels) {
      //TODO: cubit.updateEventModel(result);
    }
  }

  void _showListDetails(ChatCubit cubit, ChatState state) async {
    if (state.aiList == null) return;

    final result = await context.pushRoute(
      UpsertListItemRoute(
        parameter: state.aiList!.toUpsertListItemParameter(),
      ),
    );
    // if (result != null && result is ListItem) {
    //   cubit.updateListItem(result);r
    // }
  }

  void _navigateToVisualTripPlanner(ChatCubit cubit, ChatState state) async {
    if (state.aiTripIntent == null) return;

    // Don't close the chat cubit - just pause it temporarily
    // This allows us to resume the connection when returning from VTP
    AppLogger.d("Navigating to VTP with initialData: ${state.aiTripIntent}");

    await context.router.push(
      VisualTripPlannerRoute(
        parameter: VisualTripPlannerParameter(
          initialData: state.aiTripIntent,
        ),
      ),
    );

    // When returning from VTP, check if we need to reconnect
    AppLogger.d("Returned from VTP, checking connection state");
    if (cubit.state.connState == ConnState.none ||
        cubit.state.connState == ConnState.timeout ||
        cubit.state.connState == ConnState.done) {
      AppLogger.d("Reconnecting chat after VTP");
      await cubit.reconnect();
    }
  }

  @override
  Widget buildAppBar(BuildContext context, ChatCubit cubit, ChatState state) {
    return CustomAppBar2(
      title: LocaleKeys.chat_screen_text.tr(),
      showBack: true,
      actions: [
        // // Test button for VisualTripPlanner
        // IconButton(
        //   icon: const Icon(Icons.flight_takeoff),
        //   onPressed: () => cubit.testVisualTripPlanner(),
        // ),
        if (state.suggestions.isNotEmpty)
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showSuggestionHelpDialog,
          ),
        IconButton(
          icon: Icon(Icons.close),
          onPressed: _goBack,
        ),
      ],
    );
  }

  @override
  Widget buildBody(BuildContext context, ChatCubit cubit, ChatState state) {
    if (state.connState == ConnState.none ||
        state.connState == ConnState.timeout) {
      return state.loading
          ? const Center(child: CircularProgressIndicator())
          : _buildLostConnectionLayout(cubit);
    }

    return Column(
      children: [
        Expanded(
          child: state.loading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    _buildMessageList(cubit, state),
                    _buildInputArea(context, cubit, state),
                  ],
                ),
        ),
      ],
    );
  }

  Widget _buildMessageList(ChatCubit cubit, ChatState state) {
    return Expanded(
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(20.0),
        itemCount: state.messages.length,
        itemBuilder: (context, index) {
          final message = state.messages[index];
          final isUserMessage = message['type'] == 'user';
          final color = isUserMessage ? Colors.black : Colors.blue;

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMessageRow(message, isUserMessage, color),
                if (message.containsKey('suggestions'))
                  _buildSuggestions(message['suggestions'], cubit, state),
                if (message.containsKey('trip_available') &&
                    message['trip_available'] == true)
                  ElevatedButton(
                    onPressed: () => _showTripDetails(cubit, state),
                    child: const Text('Trip Preview'),
                  ),
                if (message.containsKey('event_available') &&
                    message['event_available'] == true)
                  ElevatedButton(
                    onPressed: () => _showEventDetails(cubit, state, message),
                    child: Text(LocaleKeys.view_event.tr()),
                  ),
                if (message.containsKey('list_available') &&
                    message['list_available'] == true)
                  ElevatedButton(
                    onPressed: () => _showListDetails(cubit, state),
                    child: const Text('List Preview'),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMessageRow(
      Map<String, dynamic> message, bool isUserMessage, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isUserMessage
            ? Icon(Icons.account_circle, color: color, size: 35)
            : Assets.images.chatbot.image(width: 35, height: 35),
        const SizedBox(width: 5),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              message['message_type'] == MessageType.audio
                  ? _buildAudioMessageView()
                  : _buildTextMessageView(message['message']!, color),
              Text(
                message['timestamp']!,
                style: AppStyle.regular10V2(color: Colors.grey),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTextMessageView(String message, Color color) {
    return SelectableText(
      message,
      style: AppStyle.regular16V2(color: color),
    );
  }

  Widget _buildAudioMessageView() {
    return GestureDetector(
      onTap: () {
        // Play audio
        AppLogger.d('Play audio pressed');
      },
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(Icons.mic, size: 24),
        ),
      ),
    );
  }

  Widget _buildSuggestions(
      List<String> suggestions, ChatCubit cubit, ChatState state) {
    // Auto-show the hint dialog
    if (!_hintShown && suggestions.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_hintShown) {
          _showSuggestionHelpDialog();
        }
      });
    }

    return Wrap(spacing: 8.0, children: [
      ...suggestions.map((suggestion) {
        return ElevatedButton(
          onPressed: () => {_handleSuggestionPressed(suggestion, cubit, state)},
          onLongPress: () => {_handleSuggestionLongPressed(suggestion)},
          style: ElevatedButton.styleFrom(
            foregroundColor: _selectedSuggestions.contains(suggestion)
                ? Colors.blueAccent
                : Colors.lightBlue,
            backgroundColor: _selectedSuggestions.contains(suggestion)
                ? Colors.grey[200]
                : Colors.white,
            textStyle: _selectedSuggestions.contains(suggestion)
                ? AppStyle.regular16V2()
                : AppStyle.regular14V2(),
          ),
          child: Text(suggestion),
        );
      }).toList()
    ]);
  }

  Widget _buildInputArea(
      BuildContext context, ChatCubit cubit, ChatState state) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          _buildMicIconView(context, cubit, state),
          state.isRecording
              ? _buildRecordingArea(context, cubit, state)
              : _buildTextArea(context, cubit, state),
          state.isRecording
              ? const SizedBox()
              : _buildAttachmentIcon(context, cubit, state),
          state.isRecording
              ? const SizedBox()
              : _buildSendIcon(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildTextArea(
      BuildContext context, ChatCubit cubit, ChatState state) {
    return Expanded(
      child: TextFormField(
        controller: _controller,
        decoration: InputDecoration(labelText: LocaleKeys.send_message.tr()),
        maxLines: null,
        keyboardType: TextInputType.multiline,
      ),
    );
  }

  Widget _buildAttachmentIcon(
      BuildContext context, ChatCubit cubit, ChatState state) {
    return IconButton(
      icon: const Icon(Icons.add),
      onPressed: () {
        cubit.pickImage();
      },
    );
  }

  Widget _buildSendIcon(
      BuildContext context, ChatCubit cubit, ChatState state) {
    return state.isWaitingForResponse == true
        ? const CircularProgressIndicator()
        : IconButton(
            icon: const Icon(Icons.send),
            onPressed: () => _sendMessage(cubit, state),
          );
  }

  Widget _buildMicIconView(context, ChatCubit cubit, ChatState state) {
    return GestureDetector(
      onLongPressMoveUpdate: state.isWaitingForResponse
          ? null
          : (details) {
              // AppLogger.d('On long press move update, delta dx: ${details.offsetFromOrigin}');
              if (state.isRecording) {
                cubit.updateSlideToCancelOffset(details.offsetFromOrigin);
              }
            },
      onLongPress: state.isWaitingForResponse
          ? null
          : () {
              // AppLogger.d('On long press');
              cubit.startListening();
            },
      onLongPressUp: state.isWaitingForResponse
          ? null
          : () {
              // AppLogger.d('On long press up');
              if (state.isRecording) {
                if (state.slideToCancelOffset.dx.abs() > 100 ||
                    state.slideToCancelOffset.dy.abs() > 100) {
                  cubit.cancelListening();
                } else {
                  cubit.stopListening();
                }
              }
            },
      onLongPressCancel: state.isWaitingForResponse
          ? null
          : () {
              // AppLogger.d('On long press cancel');
              if (state.isRecording) {
                cubit.cancelListening();
              }
            },
      child: Stack(
        children: [
          // _buildMicIconAnimation(context, cubit, state),
          // if (state.isRecording) ..._buildMicIconTranslation(context, cubit, state),
          if (state.isRecording)
            ..._buildMicIconTranslation(context, cubit, state)
          else
            _buildMicIconAnimation(context, cubit, state)
        ],
      ),
    );
  }

  Widget _buildMicIconAnimation(
      BuildContext context, ChatCubit cubit, ChatState state) {
    return AnimatedBuilder(
      animation: _micAnimationController,
      builder: (context, child) {
        return Transform.scale(
          scale: lerpDouble(
              0.8, 1.2, _micAnimationController.value)!, // Scale animation
          child: Opacity(
            opacity: state.isWaitingForResponse
                ? 0.5
                : lerpDouble(0.7, 1.0,
                    _micAnimationController.value)!, // Opacity animation
            child: CircleAvatar(
              backgroundColor: state.isRecording ? Colors.red : Colors.blue,
              radius: 40,
              child: Icon(
                state.isRecording ? Icons.mic_off : Icons.mic,
                color: Colors.white,
                size: 40,
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildMicIconTranslation(
      BuildContext context, ChatCubit cubit, ChatState state) {
    return [
      Transform.translate(
        offset: Offset(state.slideToCancelOffset.dx, 0),
        child: _buildMicIconAnimation(context, cubit, state),
      )
    ];
  }

  Widget _buildRecordingArea(
      BuildContext context, ChatCubit cubit, ChatState state) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          children: [
            FadeTransition(
              opacity: _blinkAnimation,
              child: Text(LocaleKeys.slide_to_cancel.tr()),
            ),
            const Spacer(),
            Text(
              '${(state.recordingDuration.inMinutes % 60).toString().padLeft(2, '0')}:${(state.recordingDuration.inSeconds % 60).toString().padLeft(2, '0')}',
              style: AppStyle.regular16V2(color: Colors.red),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLostConnectionLayout(ChatCubit cubit) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.signal_wifi_off,
            size: 80,
            color: Colors.red,
          ),
          const SizedBox(height: 24),
          Text(
            LocaleKeys.connection_lost.tr(),
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.unable_connect_server.tr(),
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => cubit.reconnect(),
            icon: const Icon(Icons.refresh),
            label: Text(LocaleKeys.try_again.tr()),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    _micAnimationController.dispose();
    _blinkAnimationController.dispose();
    _isTimeoutDialogShowing = false;
    super.dispose();
  }

  double? lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) {
      return null;
    }
    a ??= 0.0; // If a is null, default to 0.0
    b ??= 0.0; // If b is null, default to 0.0
    return a + (b - a) * t;
  }
}
