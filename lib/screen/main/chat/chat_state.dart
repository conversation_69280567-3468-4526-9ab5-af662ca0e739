import 'package:family_app/base/widget/cubit/base_state.dart';
import 'package:family_app/data/model/ai_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:flutter/material.dart';

enum ConnState { none, success, error, timeout, done }

enum MessageType {
  text,
  audio,
}

class ChatState extends BaseState {
  final List<Map<String, dynamic>> messages;
  final ConnState connState;
  final bool loading;
  final bool isWaitingForResponse;

  Trip? aiTrip;
  FlEvent? aiEvent;
  FlList? aiList;
  Map<String, dynamic>? aiTripIntent;

  String? base64Image;
  String? fileName;

  final String? errorMessage;
  final bool isRecording;
  final Duration recordingDuration;
  final String? audioFilePath;
  final Offset slideToCancelOffset;

  ChatState({
    this.messages = const [],
    this.connState = ConnState.none,
    this.loading = true,
    this.isWaitingForResponse = false,
    this.aiTrip,
    this.aiEvent,
    this.aiList,
    this.aiTripIntent,
    this.base64Image,
    this.fileName,
    this.errorMessage,
    this.isRecording = false,
    this.recordingDuration = Duration.zero,
    this.audioFilePath,
    this.slideToCancelOffset = const Offset(0, 0),
  });

  @override
  List<Object?> get props => [
        messages,
        connState,
        loading,
        isWaitingForResponse,
        aiTrip,
        aiEvent,
        aiList,
        aiTripIntent,
        base64Image,
        fileName,
        errorMessage,
        isRecording,
        recordingDuration,
        audioFilePath,
        slideToCancelOffset,
      ];

  ChatState copyWith({
    List<Map<String, dynamic>>? messages,
    ConnState? connState,
    bool? loading,
    bool? isWaitingForResponse,
    Trip? aiTrip,
    FlEvent? aiEvent,
    FlList? aiList,
    Map<String, dynamic>? aiTripIntent,
    String? base64Image,
    String? fileName,
    String? errorMessage,
    bool? isRecording,
    Duration? recordingDuration,
    String? audioFilePath,
    Offset? slideToCancelOffset,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      connState: connState ?? this.connState,
      loading: loading ?? this.loading,
      isWaitingForResponse: isWaitingForResponse ?? this.isWaitingForResponse,
      aiTrip: aiTrip ?? this.aiTrip,
      aiEvent: aiEvent ?? this.aiEvent,
      aiList: aiList ?? this.aiList,
      aiTripIntent: aiTripIntent ?? this.aiTripIntent,
      base64Image: base64Image ?? this.base64Image,
      fileName: fileName ?? this.fileName,
      errorMessage: errorMessage ?? this.errorMessage,
      isRecording: isRecording ?? this.isRecording,
      recordingDuration: recordingDuration ?? this.recordingDuration,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      slideToCancelOffset: slideToCancelOffset ?? this.slideToCancelOffset,
    );
  }

  ChatState clearRecordingInfo() {
    return ChatState(
      messages: messages,
      connState: connState,
      loading: loading,
      isWaitingForResponse: isWaitingForResponse,
      aiTrip: aiTrip,
      aiEvent: aiEvent,
      aiList: aiList,
      aiTripIntent: aiTripIntent,
      base64Image: base64Image,
      fileName: fileName,
      errorMessage: errorMessage,
    );
  }

  List<String> get suggestions {
    return messages.where((message) => message.containsKey('suggestions')).expand((message) => message['suggestions'] as List<String>).toList();
  }
}
